import React, {useEffect, useState} from 'react';
import {useMutation, useQuery} from '@apollo/client';
import {GET_AVAILABLE_EXCHANGES, GET_USER_SETTINGS, GET_WATCHED_ASSETS,
    GET_AVAILABLE_CATEGORIES
} from '@/lib/graphql/queries';
import {
    ADD_ASSET,
    REMOVE_ASSET,
    UPDATE_ASSET_CATEGORY,
    UPDATE_ASSET_POSITIONS,
    UPDATE_EXCHANGE,
    UPDATE_INDICATORS,
    UPDATE_INTERVAL,
    UPDATE_SELECTED_CATEGORY
} from '@/lib/graphql/mutations';

export function useDashboardLogic() {
    const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
    const [filteredAssets, setFilteredAssets] = useState<string[]>([]);
    const [assetData, setAssetData] = useState<any[]>([]);
    const [selectedInterval, setSelectedInterval] = useState('1h');
    const [selectedIndicators, setSelectedIndicators] = useState(['RSI', 'MACD', 'AO', 'WILLIAMS_R', 'VWAP']);
    const [selectedExchange, setSelectedExchange] = useState('okx');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [isAssetSelectorOpen, setIsAssetSelectorOpen] = useState(false);

    const availableIntervals = ['15m', '1h', '4h', '1d'];
    const availableIndicators = [
        'RSI', 'MACD', 'BB', 'STOCH', 'AO', 'OBV', 'EMA_12', 'EMA_26',
        // New crypto-specific indicators
        'WILLIAMS_R', 'CCI', 'ADX', 'MFI', 'VWAP'
    ];

    // Load watched assets (backend will filter by user's selected exchange)
    const {data: watchedAssets, refetch: refetchWatchedAssets} = useQuery(GET_WATCHED_ASSETS, {
        pollInterval: 60000, // Refresh every minute
    });

    // Load user settings
    const {data: userSettings} = useQuery(GET_USER_SETTINGS);

    // Load available exchanges
    const {data: availableExchangesData} = useQuery(GET_AVAILABLE_EXCHANGES);
    const availableExchanges = availableExchangesData?.availableExchanges || ['okx', 'gateio'];

    // Load available categories
    const {data: availableCategoriesData} = useQuery(GET_AVAILABLE_CATEGORIES);
    const baseCategories = availableCategoriesData?.availableCategories || ['all', 'grayscale', 'favorites'];

    // Get custom categories from asset data
    const customCategories = React.useMemo(() => {
        if (!assetData.length) return [];
        const categories = assetData
            .map(asset => asset.category)
            .filter(category => category && !baseCategories.includes(category));
        return [...new Set(categories)]; // Remove duplicates
    }, [assetData, baseCategories]);

    const availableCategories = [...baseCategories, ...customCategories];

    // Mutations
    const [addAssetMutation] = useMutation(ADD_ASSET);
    const [removeAssetMutation] = useMutation(REMOVE_ASSET);
    const [updateIntervalMutation] = useMutation(UPDATE_INTERVAL);
    const [updateIndicatorsMutation] = useMutation(UPDATE_INDICATORS);
    const [updateExchangeMutation] = useMutation(UPDATE_EXCHANGE);
    const [updateAssetPositionsMutation] = useMutation(UPDATE_ASSET_POSITIONS);
    const [updateSelectedCategoryMutation] = useMutation(UPDATE_SELECTED_CATEGORY);
    const [updateAssetCategoryMutation] = useMutation(UPDATE_ASSET_CATEGORY);

    // Update selected assets when watchedAssets changes
    // Backend already filters by user's selected exchange, so we just use all returned assets
    useEffect(() => {
        if (watchedAssets?.watchedAssets) {
            const assets = watchedAssets.watchedAssets.map((asset: { symbol: string }) => asset.symbol);
            setSelectedAssets(assets);
            setAssetData(watchedAssets.watchedAssets);
        }
    }, [watchedAssets]);

    // Load user settings when available
    useEffect(() => {
        if (userSettings?.getUserSettings) {
            setSelectedInterval(userSettings.getUserSettings.selectedInterval);
            setSelectedIndicators(userSettings.getUserSettings.selectedIndicators);
            setSelectedExchange(userSettings.getUserSettings.selectedExchange || 'okx');
            setSelectedCategory(userSettings.getUserSettings.selectedCategory || 'all');
        }
    }, [userSettings]);

    // Backend now handles category filtering, so we don't need frontend filtering
    // Keep filteredAssets for compatibility but it will be the same as selectedAssets
    useEffect(() => {
        setFilteredAssets(selectedAssets);
    }, [selectedAssets]);

    const handleIntervalChange = async (interval: string) => {
        setSelectedInterval(interval);
        try {
            await updateIntervalMutation({
                variables: {interval},
            });
        } catch (error) {
            console.error('Error updating interval:', error);
        }
    };

    const handleIndicatorToggle = async (indicator: string) => {
        const newIndicators = selectedIndicators.includes(indicator)
            ? selectedIndicators.filter(i => i !== indicator)
            : [...selectedIndicators, indicator];

        setSelectedIndicators(newIndicators);

        try {
            await updateIndicatorsMutation({
                variables: {indicators: newIndicators},
            });
        } catch (error) {
            console.error('Error updating indicators:', error);
        }
    };

    const handleExchangeChange = async (exchange: string) => {
        setSelectedExchange(exchange);
        try {
            await updateExchangeMutation({
                variables: {exchange},
            });
            // Refresh the page to reload data from the new exchange
            window.location.reload();
        } catch (error) {
            console.error('Error updating exchange:', error);
        }
    };

    const handleAssetAdd = async (symbol: string) => {
        try {
            await addAssetMutation({
                variables: {symbol},
                refetchQueries: [
                    {query: GET_WATCHED_ASSETS},
                    {query: GET_USER_SETTINGS}
                ],
            });

            // Handle category assignment based on selected category
            if (selectedCategory && selectedCategory !== 'all') {
                // Custom category - assign the asset to that category
                await handleUpdateAssetCategory(symbol, selectedCategory);
            }

            // Update local state immediately
            setSelectedAssets(prev => [...prev, symbol]);
            await refetchWatchedAssets();
        } catch (error) {
            console.error('Error adding asset:', error);
        }
    };

    const handleAssetRemove = async (symbol: string) => {
        try {
            await removeAssetMutation({
                variables: {symbol},
                refetchQueries: [
                    {query: GET_WATCHED_ASSETS},
                    {query: GET_USER_SETTINGS}
                ],
            });
            // Update local state immediately
            setSelectedAssets(prev => prev.filter(s => s !== symbol));
            await refetchWatchedAssets();
        } catch (error) {
            console.error('Error removing asset:', error);
        }
    };

    const openAssetSelector = () => setIsAssetSelectorOpen(true);
    const closeAssetSelector = () => setIsAssetSelectorOpen(false);

    const handleAssetReorder = async (newOrder: string[]) => {
        try {
            // Update local state immediately for better UX
            setSelectedAssets(newOrder);

            // Prepare position updates
            const positions = newOrder.map((symbol, index) => ({
                symbol,
                position: index,
            }));

            // Update positions in the database
            await updateAssetPositionsMutation({
                variables: {positions},
            });

            console.log('Asset positions updated successfully');
        } catch (error) {
            console.error('Error updating asset positions:', error);
            // Revert local state on error
            await refetchWatchedAssets();
        }
    };

    const handleCategoryChange = async (category: string) => {
        setSelectedCategory(category);
        try {
            await updateSelectedCategoryMutation({
                variables: {category},
            });
        } catch (error) {
            console.error('Error updating category:', error);
        }
    };

    // Removed favorites functionality - only custom categories now

    const handleUpdateAssetCategory = async (symbol: string, category: string | null) => {
        try {
            await updateAssetCategoryMutation({
                variables: {symbol, category},
                refetchQueries: [{query: GET_WATCHED_ASSETS}],
            });

            // Update local asset data
            setAssetData(prev => prev.map(asset =>
                asset.symbol === symbol
                    ? {...asset, category}
                    : asset
            ));
        } catch (error) {
            console.error('Error updating asset category:', error);
        }
    };

    return {
        selectedAssets: filteredAssets, // Return filtered assets instead of all assets
        allAssets: selectedAssets, // Keep reference to all assets for management
        assetData, // Full asset data with categories
        selectedInterval,
        selectedIndicators,
        selectedExchange,
        selectedCategory,

        availableIntervals,
        availableIndicators,
        availableExchanges,
        availableCategories,
        isAssetSelectorOpen,
        handleIntervalChange,
        handleIndicatorToggle,
        handleExchangeChange,
        handleCategoryChange,

        handleUpdateAssetCategory,
        handleAssetAdd,
        handleAssetRemove,
        handleAssetReorder,
        openAssetSelector,
        closeAssetSelector,
    };
}
