import React, {useEffect, useState} from 'react';
import {useQuery} from '@apollo/client';
import {GET_DASHBOARD_ROWS} from '@/lib/graphql/queries';
import {DashboardHeader} from './DashboardHeader';
import {AssetRow} from './AssetRow';
import {AssetSelectorModal} from './AssetSelectorModal';
import {CategoryTabs} from './CategoryTabs';
import {Tooltip} from './Tooltip';
import {UpdateIndicator} from './UpdateIndicator';
import {TradingViewModal} from './TradingViewModal';
import {AltcoinMarketBar} from './AltcoinMarketBar';
import {useDashboardLogic} from './hooks/useDashboardLogic';
import {useAltcoinMarketData} from '../../hooks/useAltcoinMarketData';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';

interface DashboardRowData {
    symbol: string;
    price: number;
    change24h: number;
    volume24h: number;
    recommendation: string;
    confidence: number;
    indicators: Array<{
        indicator: string;
        value: number;
        signal: string;
    }>;
}

const INDICATOR_DESCRIPTIONS = {
    'RSI': 'Relative Strength Index (0-100): Measures momentum. Values >70 suggest overbought conditions, <30 suggest oversold conditions.',
    'MACD': 'Moving Average Convergence Divergence: Shows relationship between two moving averages. Positive values suggest upward momentum.',
    'BB': 'Bollinger Bands: Price volatility indicator. Shows if price is relatively high or low compared to recent trades.',
    'STOCH': 'Stochastic Oscillator (0-100): Momentum indicator comparing closing price to price range. >80 overbought, <20 oversold.',
    'AO': 'Awesome Oscillator: Enhanced momentum indicator with increased weight for crypto markets. Particularly reliable for momentum-driven crypto trends.',
    'OBV': 'On-Balance Volume: Uses volume flow to predict price changes. Rising OBV suggests accumulation, falling suggests distribution.',
    'EMA_12': '12-period Exponential Moving Average: Trend-following indicator that gives more weight to recent prices.',
    'EMA_26': '26-period Exponential Moving Average: Longer-term trend indicator, slower to react than EMA_12.',
    // New crypto-specific indicators
    'WILLIAMS_R': 'Williams %R (-100 to 0): Crypto-optimized momentum oscillator. Values >-20 suggest overbought, <-80 suggest oversold conditions.',
    'CCI': 'Commodity Channel Index: Identifies cyclical trends in crypto markets. Values >100 suggest strong uptrend, <-100 suggest strong downtrend.',
    'ADX': 'Average Directional Index (0-100): Measures trend strength in volatile crypto markets. Values >25 indicate strong trends.',
    'MFI': 'Money Flow Index (0-100): Volume-weighted RSI combining price and volume. >80 overbought, <20 oversold with volume confirmation.',
    'VWAP': 'Volume Weighted Average Price: Institutional benchmark showing average price weighted by volume. Price above VWAP is bullish.'
};

export function Dashboard() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    // Altcoin market data
    const {marketData: altcoinMarketData, loading: altcoinLoading} = useAltcoinMarketData();

    // Drag and drop sensors
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const {
        selectedAssets,
        allAssets,
        selectedInterval,
        selectedIndicators,
        selectedExchange,
        selectedCategory,

        availableIntervals,
        availableIndicators,
        availableExchanges,
        availableCategories,
        handleIntervalChange,
        handleIndicatorToggle,
        handleExchangeChange,
        handleCategoryChange,

        handleUpdateAssetCategory,
        handleAssetAdd,
        handleAssetRemove,
        handleAssetReorder,
        closeAssetSelector,
    } = useDashboardLogic();

    const [openChartSymbol, setOpenChartSymbol] = useState<string | null>(null);
    const handleOpenChart = (symbol: string) => setOpenChartSymbol(symbol);
    const handleCloseChart = () => setOpenChartSymbol(null);

    function handleDragEnd(event: DragEndEvent) {
        const {active, over} = event;

        if (active.id !== over?.id) {
            const oldIndex = selectedAssets.findIndex(asset => asset === active.id);
            const newIndex = selectedAssets.findIndex(asset => asset === over?.id);

            const newOrder = arrayMove(selectedAssets, oldIndex, newIndex);
            handleAssetReorder(newOrder);
        }
    }

    // Single consolidated query for all dashboard row data
    const {data: dashboardData, loading: dashboardLoading, refetch: refetchDashboard} = useQuery(GET_DASHBOARD_ROWS, {
        variables: {
            interval: selectedInterval
        },
        pollInterval: 30000, // 30 seconds for comprehensive data
        fetchPolicy: 'cache-and-network',
        errorPolicy: 'all',
    });

    // Extract data from consolidated query
    const dashboardRows = dashboardData?.dashboardRows || [];

    // Keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.ctrlKey && (event.key === 'm' || event.key === 'M')) {
                event.preventDefault();
                setIsMenuOpen(!isMenuOpen);
            }
            if (event.key === 'Escape') {
                setIsMenuOpen(false);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [isMenuOpen]);

    return (
        <div className="min-h-screen p-2 md:p-4" style={{background: 'var(--background)', color: 'var(--foreground)'}}>
            {/* Header */}
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-2 gap-2 md:gap-0">
                <div className="w-full md:w-auto">
                    <h1 className="text-sm md:text-base font-mono" style={{color: 'var(--foreground)'}}>
                        Crypto Monitor
                    </h1>
                    <p className="text-xs font-mono break-all md:break-normal" style={{color: 'var(--secondary)'}}>
                        {selectedAssets.length}/{allAssets.length} assets • {selectedIndicators.length} indicators
                        • {selectedInterval} • {selectedExchange.toUpperCase()} • {selectedCategory}
                    </p>
                </div>
                <div className="flex items-center gap-2 md:gap-4 w-full md:w-auto justify-end">
                    <UpdateIndicator/>
                    <button
                        onClick={() => setIsMenuOpen(true)}
                        className="font-mono px-2 py-1 text-xs border transition-colors whitespace-nowrap"
                        style={{
                            borderColor: 'var(--border)',
                            color: 'var(--secondary)',
                            backgroundColor: 'transparent'
                        }}
                        onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)'}
                        onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
                    >
                        <span className="hidden md:inline">Settings (Ctrl+M)</span>
                        <span className="md:hidden">Settings</span>
                    </button>
                </div>
            </div>

            <AltcoinMarketBar marketData={altcoinMarketData}/>

            {/* Main Dashboard */}
            {dashboardRows.length === 0 ? (
                <div>
                    <div className="table-container">
                        <div className="table-wrapper">
                            {/* Altcoin Market Indicators */}

                            {/* Category Tabs */}
                            <CategoryTabs
                                categories={availableCategories}
                                selectedCategory={selectedCategory}
                                onCategoryChange={handleCategoryChange}
                                onAssetDropped={handleUpdateAssetCategory}
                            />
                        </div>
                    </div>
                    <div className="flex items-center justify-center h-32">
                        <div className="text-center font-mono">
                            <div className="text-xs mb-2" style={{color: 'var(--secondary)'}}>
                                No assets selected
                            </div>
                            <p className="text-xs" style={{color: 'var(--muted)'}}>Press &apos;Ctrl+M&apos; to add
                                crypto
                                assets</p>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="table-container">
                    <div className="table-wrapper">
                        {/* Altcoin Market Indicators */}

                        {/* Category Tabs */}
                        <CategoryTabs
                            categories={availableCategories}
                            selectedCategory={selectedCategory}
                            onCategoryChange={handleCategoryChange}
                            onAssetDropped={handleUpdateAssetCategory}
                        />

                        {/* Show message when category has no assets */}
                        {selectedAssets.length === 0 && allAssets.length > 0 ? (
                            <div className="flex items-center justify-center h-32">
                                <div className="text-center font-mono">
                                    <div className="text-xs mb-2" style={{color: 'var(--secondary)'}}>
                                        No assets in "{selectedCategory}" category
                                    </div>
                                    <p className="text-xs" style={{color: 'var(--muted)'}}>
                                        {selectedCategory === 'favorites'
                                            ? 'Click the ☆ icon on assets to add them to favorites'
                                            : selectedCategory === 'grayscale'
                                                ? 'Add Grayscale assets like BTC-USDT, ETH-USDT to see them here'
                                                : 'Switch to "All" category to see all assets'
                                        }
                                    </p>
                                </div>
                            </div>
                        ) : (
                            <>
                                <DashboardHeader indicators={selectedIndicators}/>

                                <DndContext
                                    sensors={sensors}
                                    collisionDetection={closestCenter}
                                    onDragEnd={handleDragEnd}
                                >
                                    <SortableContext
                                        items={selectedAssets}
                                        strategy={verticalListSortingStrategy}
                                    >
                                        {dashboardRows.map((rowData: DashboardRowData) => {
                                            return (
                                                <AssetRow
                                                    key={rowData.symbol}
                                                    asset={rowData.symbol}
                                                    indicators={selectedIndicators}
                                                    rowData={rowData}
                                                    loading={dashboardLoading}
                                                    onOpenChart={handleOpenChart}
                                                />
                                            );
                                        })}
                                    </SortableContext>
                                </DndContext>
                            </>
                        )}
                    </div>
                </div>
            )}

            {/* Overlay Menu */}
            {isMenuOpen && (
                <div className="overlay-menu" onClick={() => setIsMenuOpen(false)}>
                    <div className="menu-panel font-mono" onClick={(e) => e.stopPropagation()}>
                        <div className="flex items-center justify-between mb-4 pb-2 border-b"
                             style={{borderColor: 'var(--border)'}}>
                            <h2 className="text-sm font-mono" style={{color: 'var(--foreground)'}}>Settings</h2>
                            <button
                                onClick={() => setIsMenuOpen(false)}
                                className="px-2 py-1 text-xs border transition-colors"
                                style={{borderColor: 'var(--border)', color: 'var(--secondary)'}}
                                onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)'}
                                onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
                            >
                                Close
                            </button>
                        </div>

                        {/* Asset Management */}
                        <div className="mb-4">
                            <h3 className="text-sm font-mono mb-3" style={{color: 'var(--foreground)'}}>Assets</h3>
                            <AssetSelectorModal
                                isOpen={true}
                                onClose={closeAssetSelector}
                                onAssetAdd={handleAssetAdd}
                                embedded={true}
                            />

                            <div className="mt-3 space-y-1 max-h-32 overflow-y-auto">
                                {selectedAssets.map(asset => (
                                    <div key={asset} className="flex items-center justify-between py-1 text-xs"
                                         style={{borderColor: 'var(--border)'}}>
                                        <span className="font-mono" style={{color: 'var(--foreground)'}}>{asset}</span>
                                        <button
                                            onClick={() => handleAssetRemove(asset)}
                                            className="text-xs px-2 py-1 border transition-colors"
                                            style={{borderColor: 'var(--border)', color: 'var(--danger)'}}
                                            onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)'}
                                            onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
                                        >
                                            Remove
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Indicators */}
                        <div className="mb-4">
                            <h3 className="text-sm font-mono mb-3" style={{color: 'var(--foreground)'}}>Indicators</h3>
                            <div className="space-y-1">
                                {availableIndicators.map(indicator => (
                                    <Tooltip
                                        key={indicator}
                                        content={INDICATOR_DESCRIPTIONS[indicator as keyof typeof INDICATOR_DESCRIPTIONS] || indicator}
                                        position="right"
                                    >
                                        <label className="flex items-center gap-2 py-1 cursor-pointer text-xs">
                                            <input
                                                type="checkbox"
                                                checked={selectedIndicators.includes(indicator)}
                                                onChange={() => handleIndicatorToggle(indicator)}
                                                className="w-3 h-3"
                                                style={{accentColor: 'var(--primary)'}}
                                            />
                                            <span style={{color: 'var(--foreground)'}}>{indicator}</span>
                                        </label>
                                    </Tooltip>
                                ))}
                            </div>
                        </div>

                        {/* Time Interval */}
                        <div className="mb-4">
                            <h3 className="text-sm font-mono mb-3" style={{color: 'var(--foreground)'}}>Interval</h3>
                            <div className="flex gap-1">
                                {availableIntervals.map(interval => (
                                    <button
                                        key={interval}
                                        onClick={() => handleIntervalChange(interval)}
                                        className="px-2 py-1 text-xs transition-colors border"
                                        style={{
                                            borderColor: selectedInterval === interval ? 'var(--foreground)' : 'var(--border)',
                                            color: selectedInterval === interval ? 'var(--foreground)' : 'var(--secondary)',
                                            backgroundColor: selectedInterval === interval ? 'var(--highlight)' : 'transparent'
                                        }}
                                        onMouseEnter={(e) => {
                                            if (selectedInterval !== interval) {
                                                (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)';
                                            }
                                        }}
                                        onMouseLeave={(e) => {
                                            if (selectedInterval !== interval) {
                                                (e.target as HTMLElement).style.backgroundColor = 'transparent';
                                            }
                                        }}
                                    >
                                        {interval}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Exchange Selection */}
                        <div>
                            <h3 className="text-sm font-mono mb-3" style={{color: 'var(--foreground)'}}>Exchange</h3>
                            <div className="flex gap-1">
                                {availableExchanges.map((exchange: string) => (
                                    <button
                                        key={exchange}
                                        onClick={() => handleExchangeChange(exchange)}
                                        className="px-2 py-1 text-xs transition-colors border"
                                        style={{
                                            borderColor: selectedExchange === exchange ? 'var(--foreground)' : 'var(--border)',
                                            color: selectedExchange === exchange ? 'var(--foreground)' : 'var(--secondary)',
                                            backgroundColor: selectedExchange === exchange ? 'var(--highlight)' : 'transparent'
                                        }}
                                        onMouseEnter={(e) => {
                                            if (selectedExchange !== exchange) {
                                                (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)';
                                            }
                                        }}
                                        onMouseLeave={(e) => {
                                            if (selectedExchange !== exchange) {
                                                (e.target as HTMLElement).style.backgroundColor = 'transparent';
                                            }
                                        }}
                                    >
                                        {exchange === 'gateio' ? 'Gate.io' : exchange.toUpperCase()}
                                    </button>
                                ))}
                            </div>

                            <p className="text-xs mt-2" style={{color: 'var(--muted)'}}>
                                ⚠️ Changing exchange will reload the page to fetch data from the new source
                            </p>
                        </div>

                    </div>
                </div>
            )}

            {/* TradingView Modal (separate from Settings overlay) */}
            {openChartSymbol && (
                <TradingViewModal
                    symbol={openChartSymbol}
                    interval={selectedInterval}
                    exchange={selectedExchange}
                    onClose={handleCloseChart}
                />
            )}
        </div>
    );
}
